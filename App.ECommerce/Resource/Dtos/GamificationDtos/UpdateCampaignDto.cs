using System.ComponentModel.DataAnnotations;

namespace App.ECommerce.Resource.Dtos.GamificationDtos
{
    public class UpdateCampaignDto
    {
        [Required]
        public string CampaignId { get; set; }

        [Required]
        public string ShopId { get; set; }

        public string? Name { get; set; }

        public string? Description { get; set; }

        public IFormFile? Thumbnail { get; set; }

        public DateTime? StartTime { get; set; }

        public DateTime? EndTime { get; set; }

        public bool? IsActive { get; set; }
    }
}
